// Edge runtime + short revalidate for fast global cache
export const runtime = "edge";
export const revalidate = 60;

import type { Metadata } from "next";
import Player from "./player";

type Params = { params: Promise<{ type: "r" | "f"; id: string }> };

// const CFG = {
//     r: {
//         videoBase: process.env.NEXT_PUBLIC_VIDEO_BASE_R!,
//         thumbBase: process.env.NEXT_PUBLIC_THUMB_BASE_R!,
//     },
//     f: {
//         videoBase: process.env.NEXT_PUBLIC_VIDEO_BASE_F!,
//         thumbBase: process.env.NEXT_PUBLIC_THUMB_BASE_F!,
//     },
// };

const canonicalHost =
    process.env.NEXT_PUBLIC_CANONICAL_HOST || "https://content.melodyze.ai";
const siteName = process.env.NEXT_PUBLIC_SITE_NAME || "Melodyze.ai";
const videoExt = process.env.NEXT_PUBLIC_VIDEO_EXT || "mp4";
// const thumbExt = process.env.NEXT_PUBLIC_THUMB_EXT || "jpg";

function buildUrls(kind: "r" | "f", id: string) {
    // const bases = CFG[kind];
    // const videoUrl = `${bases.videoBase}/${id}.${videoExt}`;
    // const posterUrl = `${bases.thumbBase}/${id}.${thumbExt}`;
    const canonical = `${canonicalHost}/share/${kind}/${id}`;
    const videoUrl = 'https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/faded_lyrics_video.mp4'
    const posterUrl = 'https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/feed_testing_thumnail.png'
    return { videoUrl, posterUrl, canonical };
}

export async function generateMetadata({ params }: Params): Promise<Metadata> {
    const { type, id } = await params;
    const { videoUrl, posterUrl, canonical } = buildUrls(type, id);

    const title = `Melodyze Creators`;
    const desc = `Create, transform & share your custom covers.`;

    return {
        title,
        description: desc,
        alternates: { canonical },
        openGraph: {
            type: "video.other",
            url: canonical,
            siteName,
            title,
            description: desc,
            images: [{ url: posterUrl, width: 1200, height: 630, alt: "Melodyze preview" }],
        },
        twitter: {
            card: "summary_large_image",
            title,
            description: desc,
            images: [posterUrl],
        },
        // Extra OG video tags many scrapers read
        other: {
            "og:video": videoUrl,
            "og:video:secure_url": videoUrl,
            "og:video:type": `video/${videoExt}`,
            "og:video:width": "1280",
            "og:video:height": "720",
        },
    };
}

export default async function SharePage({ params }: Params) {
    const { type, id } = await params;
    const { videoUrl, posterUrl } = buildUrls(type, id);

    // Optional: different headings/descriptions by type
    const title = "Amazing Melodyze Creation";
    const description = "Experience the future of music creation with Melodyze.";

    return (
        <div className="container">
            {/* Loading & error handled inside <Player/> */}
            <Player
                videoUrl={videoUrl}
                posterUrl={posterUrl}
                title={title}
                description={description}
            />
        </div>
    );
}
