"use client";
import { useEffect, useRef, useState, useCallback } from "react";

export default function Player({
    videoUrl,
    posterUrl,
    title,
    description,
}: {
    videoUrl: string;
    posterUrl: string;
    title: string;
    description: string;
}) {
    const videoRef = useRef<HTMLVideoElement>(null);
    const playPauseBtnRef = useRef<HTMLButtonElement>(null);
    const muteBtnRef = useRef<HTMLButtonElement>(null);
    const progressRef = useRef<HTMLDivElement>(null);
    const videoCardRef = useRef<HTMLDivElement>(null);

    const [ready, setReady] = useState(false);
    const [errorMsg, setErrorMsg] = useState<string | null>(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [isMuted, setIsMuted] = useState(false);

const showControls = useCallback(() => {
    playPauseBtnRef.current?.classList.add("show");
    videoCardRef.current?.classList.remove("hide-controls");
    // hide after 500ms if playing
    window.setTimeout(() => {
        if (isPlaying) {
            playPauseBtnRef.current?.classList.remove("show");
            videoCardRef.current?.classList.add("hide-controls");
        }
    }, 500);
}, [isPlaying]);

const togglePlayPause = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;
    if (video.paused) {
        video.play().catch(() => { });
    } else {
        video.pause();
    }
}, []);

const toggleMute = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;
    video.muted = !video.muted;
    setIsMuted(video.muted);
}, []);

// wire events
useEffect(() => {
    const video = videoRef.current!;
    if (!video) return;

    const onTime = () => {
        if (!video.duration) return;
        const pct = (video.currentTime / video.duration) * 100;
        if (progressRef.current) progressRef.current.style.width = pct + "%";
    };
    const onPlay = () => setIsPlaying(true);
    const onPause = () => setIsPlaying(false);
    const onCanPlay = async () => {
        setReady(true);
        try {
            await video.play();
        } catch {
            // user gesture required
            showControls();
        }
    };
    const onErr = () => setErrorMsg("Failed to load video");

    video.addEventListener("timeupdate", onTime);
    video.addEventListener("play", onPlay);
    video.addEventListener("pause", onPause);
    video.addEventListener("canplay", onCanPlay);
    video.addEventListener("error", onErr);

    // mobile inline hints
    video.setAttribute("playsinline", "true");
    // iOS - properly type the webkit property
    const webkitVideo = video as HTMLVideoElement & { webkitPlaysInline?: boolean };
    webkitVideo.webkitPlaysInline = true;

    return () => {
        video.removeEventListener("timeupdate", onTime);
        video.removeEventListener("play", onPlay);
        video.removeEventListener("pause", onPause);
        video.removeEventListener("canplay", onCanPlay);
        video.removeEventListener("error", onErr);
    };
}, [showControls]); // Added showControls to dependency array

// keyboard
useEffect(() => {
    const onKey = (e: KeyboardEvent) => {
        if (e.code === "Space") {
            e.preventDefault();
            showControls();
            togglePlayPause();
        }
        if (e.code === "KeyM") {
            e.preventDefault();
            toggleMute();
        }
        if (e.code === "Escape" && document.fullscreenElement) {
            document.exitFullscreen();
        }
    };
    document.addEventListener("keydown", onKey);
    return () => document.removeEventListener("keydown", onKey);
}, [showControls, togglePlayPause, toggleMute]); // Added all dependencies

    return (
        <>
            {/* Loading */}
            {!ready && !errorMsg && (
                <div id="loading-screen" className="loading-screen">
                    <div className="loading-spinner"></div>
                    <p>Loading content...</p>
                </div>
            )}

            {/* Error */}
            {errorMsg && (
                <div id="error-screen" className="error-screen" style={{ display: "flex" }}>
                    <div className="error-content">
                        <h2>Content Not Found</h2>
                        <p>{errorMsg}</p>
                        <button onClick={() => history.back()} className="retry-button">Go Back</button>
                    </div>
                </div>
            )}

            {/* Main */}
            <div className={`main-content ${ready && !errorMsg ? "ready" : ""}`} id="main-content">
                <div className="row">
                    {/* Video */}
                    <div className="video-card" id="video-card" ref={videoCardRef}>
                        <video
                            id="main-video"
                            className="main-video"
                            preload="metadata"
                            playsInline
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                showControls();
                                togglePlayPause();
                            }}
                            ref={videoRef}
                        >
                            <source src={videoUrl} type={`video/${(process.env.NEXT_PUBLIC_VIDEO_EXT || "mp4")}`} />
                            {/* Optional webm: <source src={videoUrl.replace(".mp4",".webm")} type="video/webm" /> */}
                            Your browser does not support the video tag.
                        </video>

                        <div className="video-controls">
                            <button
                                className="play-pause-btn"
                                id="play-pause-btn"
                                aria-label="Play/Pause"
                                ref={playPauseBtnRef}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    togglePlayPause();
                                }}
                            >
                                <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor" style={{ display: isPlaying ? "none" : "block" }}>
                                    <path d="M8 5v14l11-7z" />
                                </svg>
                                <svg className="pause-icon" viewBox="0 0 24 24" fill="currentColor" style={{ display: isPlaying ? "block" : "none" }}>
                                    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
                                </svg>
                            </button>

                            <button
                                className="mute-btn"
                                id="mute-btn"
                                aria-label="Mute/Unmute"
                                ref={muteBtnRef}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleMute();
                                }}
                            >
                                <svg className="unmute-icon" viewBox="0 0 24 24" fill="currentColor" style={{ display: isMuted ? "none" : "block" }}>
                                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" />
                                </svg>
                                <svg className="mute-icon" viewBox="0 0 24 24" fill="currentColor" style={{ display: isMuted ? "block" : "none" }}>
                                    <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z" />
                                </svg>
                            </button>
                        </div>

                        <div className="progress-container">
                            <div className="progress-bar" id="progress-bar" ref={progressRef}></div>
                        </div>

                        <div className="mobile-signup" id="mobile-signup">
                            <button className="btn btn-primary" onClick={() => window.open("https://melodyze.ai", "_blank")}>
                                Sign up with Melodyze
                            </button>
                        </div>
                    </div>

                    {/* Metadata card (desktop) */}
                    <div className="metadata-card" id="metadata-card">
                        <div className="video-metadata">
                            <h1 id="video-title" style={{ fontFamily: "var(--font-iceland)" }}>{title}</h1>
                            <p id="video-description">{description}</p>
                        </div>
                        <div className="signup-section">
                            <h3>Join Melodyze</h3>
                            <p>Create, transform & share your music.</p>
                            <button className="btn btn-primary" onClick={() => window.open("https://melodyze.ai", "_blank")}>
                                Sign up with Melodyze
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
