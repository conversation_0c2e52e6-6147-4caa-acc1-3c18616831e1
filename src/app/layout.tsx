import type { Metadata } from "next";
import "./globals.css";
import { inter, iceland } from "./fonts";

const siteName = process.env.NEXT_PUBLIC_SITE_NAME || "Melodyze.ai";
const canonicalHost =
  process.env.NEXT_PUBLIC_CANONICAL_HOST || "https://content.melodyze.ai";

export const metadata: Metadata = {
  title: "Melodyze Creators",
  description: "Create, transform & share your custom covers.",
  themeColor: "#111827",
  icons: {
    icon: [
      { url: "https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/melodyze-logo.png", sizes: "32x32" },
      { url: "https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/melodyze-logo.png", sizes: "192x192" },
    ],
    apple: "https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/melodyze-logo.png",
  },
  openGraph: {
    type: "website",
    siteName,
    url: canonicalHost,
    title: "Melodyze Creators",
    description: "Create, transform & share your custom covers.",
  },
  twitter: {
    card: "summary_large_image",
    site: "@melodyzeai",
    title: "Melodyze Creators",
    description: "Create, transform & share your custom covers.",
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={`${inter.variable} ${iceland.variable}`} style={{ fontFamily: "var(--font-inter)" }}>
        {children}
      </body>
    </html>
  );
}

